import React, { useState, useEffect } from 'react'
import { supabase } from '../lib/supabase'
import { useAuth } from '../contexts/AuthContext'
import { useViewMode } from '../hooks/useViewMode'

const WorkOrderManagement = () => {
  const { user: _user } = useAuth()
  const { viewMode, setViewMode, isLoading: _viewModeLoading } = useViewMode('workorders', 'card')
  const [workOrders, setWorkOrders] = useState([])
  const [buildings, setBuildings] = useState([])
  const [_equipment, setEquipment] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)
  const [selectedStatus, setSelectedStatus] = useState('all')
  const [selectedPriority, setSelectedPriority] = useState('all')
  const [selectedBuilding, setSelectedBuilding] = useState('all')
  const [_showCreateModal, setShowCreateModal] = useState(false)

  const statuses = ['open', 'assigned', 'in_progress', 'completed', 'cancelled']
  const priorities = ['low', 'medium', 'high', 'critical']
  const _workTypes = ['preventive', 'corrective', 'emergency', 'inspection']

  useEffect(() => {
    fetchData()
  }, [])

  const fetchData = async () => {
    try {
      setLoading(true)

      // Fetch buildings
      const { data: buildingsData, error: buildingsError } = await supabase
        .from('buildings')
        .select('*')
        .eq('is_active', true)
        .order('name')

      if (buildingsError) throw buildingsError
      setBuildings(buildingsData || [])

      // Fetch equipment
      const { data: equipmentData, error: equipmentError } = await supabase
        .from('equipment')
        .select('id, category, equipment_type, building_id, location')
        .eq('status', 'active')
        .order('category')

      if (equipmentError) throw equipmentError
      setEquipment(equipmentData || [])

      // Fetch work orders with related data (excluding user joins for now)
      const { data: workOrdersData, error: workOrdersError } = await supabase
        .from('work_orders')
        .select(`
          *,
          building:buildings(name, building_code),
          equipment:equipment(category, equipment_type, location),
          alarm:alarm_notifications(subject, severity_id)
        `)
        .order('created_at', { ascending: false })

      if (workOrdersError) throw workOrdersError

      // Get unique user IDs from work orders
      const userIds = new Set()
      workOrdersData?.forEach(order => {
        if (order.assigned_to) userIds.add(order.assigned_to)
        if (order.requested_by) userIds.add(order.requested_by)
      })

      // Fetch user profiles for the user IDs
      const userMap = new Map()
      if (userIds.size > 0) {
        const { data: userProfiles, error: userProfilesError } = await supabase
          .from('user_profiles')
          .select('user_id, email, display_name, first_name, last_name')
          .in('user_id', Array.from(userIds))

        if (userProfilesError) {
          console.warn('Could not fetch user profiles:', userProfilesError)
        } else {
          userProfiles?.forEach(profile => {
            userMap.set(profile.user_id, profile)
          })
        }
      }

      // Merge user data with work orders
      const workOrdersWithUsers = workOrdersData?.map(order => ({
        ...order,
        assigned_user: order.assigned_to ? userMap.get(order.assigned_to) : null,
        requested_user: order.requested_by ? userMap.get(order.requested_by) : null
      })) || []

      setWorkOrders(workOrdersWithUsers)

    } catch (err) {
      console.error('Error fetching data:', err)
      setError(err.message)
    } finally {
      setLoading(false)
    }
  }

  const filteredWorkOrders = workOrders.filter(order => {
    const statusMatch = selectedStatus === 'all' || order.status === selectedStatus
    const priorityMatch = selectedPriority === 'all' || order.priority === selectedPriority
    const buildingMatch = selectedBuilding === 'all' || order.building_id === selectedBuilding
    return statusMatch && priorityMatch && buildingMatch
  })

  const getStatusColor = (status) => {
    switch (status) {
      case 'open': return '#6b7280'
      case 'assigned': return '#3b82f6'
      case 'in_progress': return '#f59e0b'
      case 'completed': return '#22c55e'
      case 'cancelled': return '#ef4444'
      default: return '#6b7280'
    }
  }

  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'low': return '#22c55e'
      case 'medium': return '#f59e0b'
      case 'high': return '#ef4444'
      case 'critical': return '#dc2626'
      default: return '#6b7280'
    }
  }

  const getStatusIcon = (status) => {
    switch (status) {
      case 'open': return '📋'
      case 'assigned': return '👤'
      case 'in_progress': return '🔧'
      case 'completed': return '✅'
      case 'cancelled': return '❌'
      default: return '❓'
    }
  }

  const getPriorityIcon = (priority) => {
    switch (priority) {
      case 'low': return '🟢'
      case 'medium': return '🟡'
      case 'high': return '🟠'
      case 'critical': return '🔴'
      default: return '⚪'
    }
  }

  const formatUserName = (user) => {
    if (!user) return null

    // Try display_name first, then first_name + last_name, then email
    if (user.display_name) return user.display_name
    if (user.first_name && user.last_name) return `${user.first_name} ${user.last_name}`
    if (user.first_name) return user.first_name
    return user.email
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading work orders...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-4 m-4">
        <h3 className="text-red-800 font-medium">Error Loading Work Orders</h3>
        <p className="text-red-600 mt-1">{error}</p>
      </div>
    )
  }

  return (
    <div className="p-3 sm:p-4 max-w-full mx-auto">
      {/* Header */}
      <div className="mb-6 flex flex-col sm:flex-row sm:justify-between sm:items-start gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Work Order Management</h1>
          <p className="text-gray-600">Track and manage maintenance work orders</p>
        </div>

        <div className="flex flex-col sm:flex-row items-start sm:items-center gap-3">
          {/* View Toggle */}
          <div className="flex items-center bg-gray-100 rounded-lg p-1">
            <button
              onClick={() => setViewMode('card')}
              className={`flex items-center gap-1 sm:gap-2 px-2 sm:px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                viewMode === 'card'
                  ? 'bg-white text-gray-900 shadow-sm'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" />
              </svg>
              <span className="hidden sm:inline">Cards</span>
            </button>

            <button
              onClick={() => setViewMode('table')}
              className={`flex items-center gap-1 sm:gap-2 px-2 sm:px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                viewMode === 'table'
                  ? 'bg-white text-gray-900 shadow-sm'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 10h16M4 14h16M4 18h16" />
              </svg>
              <span className="hidden sm:inline">Table</span>
            </button>
          </div>

          <button
            onClick={() => setShowCreateModal(true)}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
          >
            Create Work Order
          </button>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <div className="bg-white rounded-lg shadow p-4 border-l-4 border-blue-500">
          <h3 className="text-sm font-medium text-gray-500">Total Orders</h3>
          <p className="text-2xl font-bold text-gray-900">{workOrders.length}</p>
        </div>
        
        <div className="bg-white rounded-lg shadow p-4 border-l-4 border-yellow-500">
          <h3 className="text-sm font-medium text-gray-500">In Progress</h3>
          <p className="text-2xl font-bold text-yellow-600">
            {workOrders.filter(wo => wo.status === 'in_progress').length}
          </p>
        </div>
        
        <div className="bg-white rounded-lg shadow p-4 border-l-4 border-red-500">
          <h3 className="text-sm font-medium text-gray-500">Critical Priority</h3>
          <p className="text-2xl font-bold text-red-600">
            {workOrders.filter(wo => wo.priority === 'critical' && wo.status !== 'completed').length}
          </p>
        </div>
        
        <div className="bg-white rounded-lg shadow p-4 border-l-4 border-green-500">
          <h3 className="text-sm font-medium text-gray-500">Completed</h3>
          <p className="text-2xl font-bold text-green-600">
            {workOrders.filter(wo => wo.status === 'completed').length}
          </p>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white rounded-lg shadow p-4 mb-6">
        <div className="flex flex-wrap gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Status</label>
            <select
              value={selectedStatus}
              onChange={(e) => setSelectedStatus(e.target.value)}
              className="border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="all">All Statuses</option>
              {statuses.map(status => (
                <option key={status} value={status}>
                  {getStatusIcon(status)} {status.replace('_', ' ').toUpperCase()}
                </option>
              ))}
            </select>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Priority</label>
            <select
              value={selectedPriority}
              onChange={(e) => setSelectedPriority(e.target.value)}
              className="border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="all">All Priorities</option>
              {priorities.map(priority => (
                <option key={priority} value={priority}>
                  {getPriorityIcon(priority)} {priority.toUpperCase()}
                </option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Building</label>
            <select
              value={selectedBuilding}
              onChange={(e) => setSelectedBuilding(e.target.value)}
              className="border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="all">All Buildings</option>
              {buildings.map(building => (
                <option key={building.id} value={building.id}>
                  {building.name} ({building.building_code})
                </option>
              ))}
            </select>
          </div>
        </div>
      </div>

      {/* Work Orders List */}
      <div className="bg-white rounded-lg shadow overflow-hidden">
        <div className="px-4 py-3 border-b border-gray-200">
          <h2 className="text-lg font-medium text-gray-900">
            Work Orders ({filteredWorkOrders.length})
          </h2>
        </div>

        {filteredWorkOrders.length === 0 ? (
          <div className="p-8 text-center text-gray-500">
            <p>No work orders found matching the selected criteria.</p>
          </div>
        ) : viewMode === 'card' ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 p-4">
            {filteredWorkOrders.map(order => (
              <WorkOrderCard
                key={order.id}
                workOrder={order}
                getStatusColor={getStatusColor}
                getPriorityColor={getPriorityColor}
                getStatusIcon={getStatusIcon}
                getPriorityIcon={getPriorityIcon}
                formatUserName={formatUserName}
              />
            ))}
          </div>
        ) : (
          <WorkOrderTable
            workOrders={filteredWorkOrders}
            getStatusColor={getStatusColor}
            getPriorityColor={getPriorityColor}
            getStatusIcon={getStatusIcon}
            getPriorityIcon={getPriorityIcon}
            formatUserName={formatUserName}
          />
        )}
      </div>
    </div>
  )
}

// Work Order Card Component
const WorkOrderCard = ({ workOrder, getStatusColor, getPriorityColor, getStatusIcon, getPriorityIcon, formatUserName }) => {
  return (
    <div className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
      <div className="flex items-start justify-between mb-3">
        <div className="flex items-center gap-2">
          <span className="text-lg">{getStatusIcon(workOrder.status)}</span>
          <div>
            <h3 className="font-medium text-gray-900">{workOrder.title}</h3>
            <p className="text-sm text-gray-500">{workOrder.work_type?.toUpperCase()}</p>
          </div>
        </div>
        <div className="flex flex-col gap-1">
          <span
            className="px-2 py-1 text-xs font-medium rounded-full text-white"
            style={{ backgroundColor: getStatusColor(workOrder.status) }}
          >
            {workOrder.status.replace('_', ' ').toUpperCase()}
          </span>
          <span
            className="px-2 py-1 text-xs font-medium rounded-full text-white text-center"
            style={{ backgroundColor: getPriorityColor(workOrder.priority) }}
          >
            {getPriorityIcon(workOrder.priority)} {workOrder.priority.toUpperCase()}
          </span>
        </div>
      </div>

      <div className="space-y-2 text-sm text-gray-600 mb-3">
        <div>
          <strong>Building:</strong> {workOrder.building?.name || 'Unknown'}
        </div>
        {workOrder.equipment && (
          <div>
            <strong>Equipment:</strong> {workOrder.equipment.category} ({workOrder.equipment.equipment_type})
          </div>
        )}
        {workOrder.assigned_user && (
          <div>
            <strong>Assigned to:</strong> {formatUserName(workOrder.assigned_user)}
          </div>
        )}
        <div>
          <strong>Created:</strong> {new Date(workOrder.created_at).toLocaleDateString()}
        </div>
        {workOrder.scheduled_date && (
          <div>
            <strong>Scheduled:</strong> {new Date(workOrder.scheduled_date).toLocaleDateString()}
          </div>
        )}
      </div>

      {workOrder.description && (
        <div className="bg-gray-50 rounded p-3 mb-3">
          <p className="text-sm text-gray-700 line-clamp-3">{workOrder.description}</p>
        </div>
      )}

      <div className="flex gap-2">
        <button className="flex-1 px-3 py-1 text-sm bg-blue-100 text-blue-800 rounded hover:bg-blue-200">
          View Details
        </button>
        <button className="flex-1 px-3 py-1 text-sm bg-gray-100 text-gray-800 rounded hover:bg-gray-200">
          Edit
        </button>
      </div>
    </div>
  )
}

// Work Order Table Component
const WorkOrderTable = ({ workOrders, getStatusColor, getPriorityColor, getStatusIcon, getPriorityIcon, formatUserName }) => {
  return (
    <div className="overflow-x-auto">
      <table className="min-w-full divide-y divide-gray-200">
        <thead className="bg-gray-50">
          <tr>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Work Order
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Building
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Priority
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Status
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Assigned To
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Created
            </th>
            <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
              Actions
            </th>
          </tr>
        </thead>
        <tbody className="bg-white divide-y divide-gray-200">
          {workOrders.map(order => (
            <tr key={order.id} className="hover:bg-gray-50">
              <td className="px-6 py-4 whitespace-nowrap">
                <div className="flex items-center">
                  <span className="mr-2">{getStatusIcon(order.status)}</span>
                  <div>
                    <div className="text-sm font-medium text-gray-900">{order.title}</div>
                    <div className="text-sm text-gray-500">{order.work_type?.toUpperCase()}</div>
                    {order.equipment && (
                      <div className="text-xs text-gray-400">
                        {order.equipment.category} - {order.equipment.equipment_type}
                      </div>
                    )}
                  </div>
                </div>
              </td>

              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                <div>{order.building?.name || 'Unknown'}</div>
                <div className="text-gray-500 text-xs">{order.building?.building_code}</div>
              </td>

              <td className="px-6 py-4 whitespace-nowrap">
                <span
                  className="inline-flex px-2 py-1 text-xs font-semibold rounded-full text-white"
                  style={{ backgroundColor: getPriorityColor(order.priority) }}
                >
                  {getPriorityIcon(order.priority)} {order.priority.toUpperCase()}
                </span>
              </td>

              <td className="px-6 py-4 whitespace-nowrap">
                <span
                  className="inline-flex px-2 py-1 text-xs font-semibold rounded-full text-white"
                  style={{ backgroundColor: getStatusColor(order.status) }}
                >
                  {order.status.replace('_', ' ').toUpperCase()}
                </span>
              </td>

              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                {order.assigned_user ? (
                  <div>{formatUserName(order.assigned_user)}</div>
                ) : (
                  <span className="text-gray-400">Unassigned</span>
                )}
              </td>

              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                <div>{new Date(order.created_at).toLocaleDateString()}</div>
                <div className="text-gray-500 text-xs">
                  {new Date(order.created_at).toLocaleTimeString()}
                </div>
              </td>

              <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                <div className="flex justify-end gap-2">
                  <button className="text-blue-600 hover:text-blue-900 text-xs px-2 py-1 border border-blue-300 rounded hover:bg-blue-50">
                    View
                  </button>
                  <button className="text-gray-600 hover:text-gray-900 text-xs px-2 py-1 border border-gray-300 rounded hover:bg-gray-50">
                    Edit
                  </button>
                </div>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  )
}

export default WorkOrderManagement
