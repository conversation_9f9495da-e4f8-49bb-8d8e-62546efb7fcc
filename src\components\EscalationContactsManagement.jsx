import React, { useState, useEffect } from 'react'
import { supabase } from '../lib/supabase'
import { useAuth } from '../contexts/AuthContext'
import retellAI from '../lib/retellAI'

/**
 * Escalation Contacts Management Component
 * Allows CRUD operations on escalation contacts for buildings
 */
const EscalationContactsManagement = () => {
  const { user: _user } = useAuth()
  const [contacts, setContacts] = useState([])
  const [buildings, setBuildings] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)
  const [selectedBuilding, setSelectedBuilding] = useState('all')
  const [showAddModal, setShowAddModal] = useState(false)
  const [editingContact, setEditingContact] = useState(null)

  // Form state
  const [formData, setFormData] = useState({
    building_id: '',
    contact_name: '',
    contact_phone: '',
    contact_email: '',
    contact_role: 'facility_manager',
    priority_level: 1,
    is_active: true,
    notification_preferences: {
      voice_calls: true,
      sms: false,
      email: true
    },
    notes: ''
  })

  // Fetch data on component mount
  useEffect(() => {
    fetchData()
  }, [])

  const fetchData = async () => {
    try {
      setLoading(true)
      
      const [contactsResult, buildingsResult] = await Promise.all([
        supabase
          .from('escalation_contacts')
          .select(`
            *,
            building:buildings(*)
          `)
          .order('building_id')
          .order('priority_level'),
        
        supabase
          .from('buildings')
          .select('*')
          .eq('is_active', true)
          .order('name')
      ])

      if (contactsResult.error) throw contactsResult.error
      if (buildingsResult.error) throw buildingsResult.error

      setContacts(contactsResult.data || [])
      setBuildings(buildingsResult.data || [])
      
    } catch (err) {
      console.error('Error fetching escalation contacts:', err)
      setError(err.message)
    } finally {
      setLoading(false)
    }
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    
    try {
      // Validate phone number
      const formattedPhone = retellAI.formatPhoneNumber(formData.contact_phone)
      if (!retellAI.isValidPhoneNumber(formattedPhone)) {
        throw new Error('Invalid phone number format. Please use E.164 format (e.g., +1234567890)')
      }

      const contactData = {
        ...formData,
        contact_phone: formattedPhone
      }

      let result
      if (editingContact) {
        // Update existing contact
        result = await supabase
          .from('escalation_contacts')
          .update(contactData)
          .eq('id', editingContact.id)
          .select(`
            *,
            building:buildings(*)
          `)
      } else {
        // Create new contact
        result = await supabase
          .from('escalation_contacts')
          .insert(contactData)
          .select(`
            *,
            building:buildings(*)
          `)
      }

      if (result.error) throw result.error

      // Update local state
      if (editingContact) {
        setContacts(prev => prev.map(contact =>
          contact.id === editingContact.id ? result.data[0] : contact
        ))
      } else {
        setContacts(prev => [...prev, result.data[0]])
      }

      // Reset form
      resetForm()
      
    } catch (err) {
      console.error('Error saving contact:', err)
      setError(err.message)
    }
  }

  const handleEdit = (contact) => {
    setEditingContact(contact)
    setFormData({
      building_id: contact.building_id,
      contact_name: contact.contact_name,
      contact_phone: contact.contact_phone,
      contact_email: contact.contact_email || '',
      contact_role: contact.contact_role,
      priority_level: contact.priority_level,
      is_active: contact.is_active,
      notification_preferences: contact.notification_preferences || {
        voice_calls: true,
        sms: false,
        email: true
      },
      notes: contact.notes || ''
    })
    setShowAddModal(true)
  }

  const handleDelete = async (contactId) => {
    if (!confirm('Are you sure you want to delete this escalation contact?')) {
      return
    }

    try {
      const { error } = await supabase
        .from('escalation_contacts')
        .delete()
        .eq('id', contactId)

      if (error) throw error

      setContacts(prev => prev.filter(contact => contact.id !== contactId))
      
    } catch (err) {
      console.error('Error deleting contact:', err)
      setError(err.message)
    }
  }

  const resetForm = () => {
    setFormData({
      building_id: '',
      contact_name: '',
      contact_phone: '',
      contact_email: '',
      contact_role: 'facility_manager',
      priority_level: 1,
      is_active: true,
      notification_preferences: {
        voice_calls: true,
        sms: false,
        email: true
      },
      notes: ''
    })
    setEditingContact(null)
    setShowAddModal(false)
  }

  // Filter contacts by selected building
  const filteredContacts = selectedBuilding === 'all' 
    ? contacts 
    : contacts.filter(contact => contact.building_id === selectedBuilding)

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Escalation Contacts</h1>
          <p className="text-gray-600">Manage call-out contacts for alarm escalation</p>
        </div>
        <button
          onClick={() => setShowAddModal(true)}
          className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700"
        >
          Add Contact
        </button>
      </div>

      {/* Retell AI Configuration Status */}
      <div className={`p-4 rounded-lg ${retellAI.isConfigured() ? 'bg-green-50 border border-green-200' : 'bg-yellow-50 border border-yellow-200'}`}>
        <div className="flex items-center">
          <div className={`w-3 h-3 rounded-full mr-3 ${retellAI.isConfigured() ? 'bg-green-500' : 'bg-yellow-500'}`}></div>
          <span className={`font-medium ${retellAI.isConfigured() ? 'text-green-800' : 'text-yellow-800'}`}>
            Retell AI: {retellAI.isConfigured() ? 'Configured' : 'Not Configured'}
          </span>
        </div>
        {!retellAI.isConfigured() && (
          <p className="text-yellow-700 text-sm mt-2">
            Configure Retell AI in .env.local to enable voice calling functionality
          </p>
        )}
      </div>

      {/* Filters */}
      <div className="flex space-x-4">
        <select
          value={selectedBuilding}
          onChange={(e) => setSelectedBuilding(e.target.value)}
          className="border border-gray-300 rounded-md px-3 py-2"
        >
          <option value="all">All Buildings</option>
          {buildings.map(building => (
            <option key={building.id} value={building.id}>
              {building.name}
            </option>
          ))}
        </select>
      </div>

      {/* Error Display */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <p className="text-red-800">{error}</p>
          <button
            onClick={() => setError(null)}
            className="text-red-600 hover:text-red-800 text-sm mt-2"
          >
            Dismiss
          </button>
        </div>
      )}

      {/* Contacts Table */}
      <div className="bg-white shadow rounded-lg overflow-hidden">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Contact
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Building
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Role
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Priority
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Phone
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Status
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {filteredContacts.map(contact => (
              <tr key={contact.id}>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div>
                    <div className="text-sm font-medium text-gray-900">
                      {contact.contact_name}
                    </div>
                    {contact.contact_email && (
                      <div className="text-sm text-gray-500">
                        {contact.contact_email}
                      </div>
                    )}
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {contact.building?.name || 'Unknown'}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {contact.contact_role.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {contact.priority_level}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {contact.contact_phone}
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                    contact.is_active 
                      ? 'bg-green-100 text-green-800' 
                      : 'bg-red-100 text-red-800'
                  }`}>
                    {contact.is_active ? 'Active' : 'Inactive'}
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                  <button
                    onClick={() => handleEdit(contact)}
                    className="text-blue-600 hover:text-blue-900"
                  >
                    Edit
                  </button>
                  <button
                    onClick={() => handleDelete(contact.id)}
                    className="text-red-600 hover:text-red-900"
                  >
                    Delete
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>

        {filteredContacts.length === 0 && (
          <div className="text-center py-8 text-gray-500">
            No escalation contacts found. Add some contacts to get started.
          </div>
        )}
      </div>

      {/* Add/Edit Modal */}
      {showAddModal && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div className="mt-3">
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                {editingContact ? 'Edit Contact' : 'Add New Contact'}
              </h3>
              
              <form onSubmit={handleSubmit} className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700">Building</label>
                  <select
                    value={formData.building_id}
                    onChange={(e) => setFormData(prev => ({ ...prev, building_id: e.target.value }))}
                    required
                    className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2"
                  >
                    <option value="">Select Building</option>
                    {buildings.map(building => (
                      <option key={building.id} value={building.id}>
                        {building.name}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700">Contact Name</label>
                  <input
                    type="text"
                    value={formData.contact_name}
                    onChange={(e) => setFormData(prev => ({ ...prev, contact_name: e.target.value }))}
                    required
                    className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700">Phone Number</label>
                  <input
                    type="tel"
                    value={formData.contact_phone}
                    onChange={(e) => setFormData(prev => ({ ...prev, contact_phone: e.target.value }))}
                    placeholder="+1234567890"
                    required
                    className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2"
                  />
                  <p className="text-xs text-gray-500 mt-1">Use E.164 format (e.g., +1234567890)</p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700">Email (Optional)</label>
                  <input
                    type="email"
                    value={formData.contact_email}
                    onChange={(e) => setFormData(prev => ({ ...prev, contact_email: e.target.value }))}
                    className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700">Role</label>
                  <select
                    value={formData.contact_role}
                    onChange={(e) => setFormData(prev => ({ ...prev, contact_role: e.target.value }))}
                    className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2"
                  >
                    <option value="facility_manager">Facility Manager</option>
                    <option value="security">Security</option>
                    <option value="maintenance">Maintenance</option>
                    <option value="emergency_contact">Emergency Contact</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700">Priority Level</label>
                  <input
                    type="number"
                    min="1"
                    max="10"
                    value={formData.priority_level}
                    onChange={(e) => setFormData(prev => ({ ...prev, priority_level: parseInt(e.target.value) }))}
                    className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2"
                  />
                  <p className="text-xs text-gray-500 mt-1">1 = highest priority</p>
                </div>

                <div>
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={formData.is_active}
                      onChange={(e) => setFormData(prev => ({ ...prev, is_active: e.target.checked }))}
                      className="mr-2"
                    />
                    <span className="text-sm font-medium text-gray-700">Active</span>
                  </label>
                </div>

                <div className="flex justify-end space-x-3 pt-4">
                  <button
                    type="button"
                    onClick={resetForm}
                    className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    className="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700"
                  >
                    {editingContact ? 'Update' : 'Create'}
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default EscalationContactsManagement
