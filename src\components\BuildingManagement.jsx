import React, { useState, useEffect } from 'react'
import { useBuildings } from '../hooks/useBuildings'
import { useAuth } from '../contexts/AuthContext'
import {
  validateBuildingForm,
  cleanBuildingFormData,
  formatPhoneNumber,
  generateBuildingCodeSuggestion,
  autoSaveFormData,
  loadAutoSavedFormData,
  clearAutoSavedFormData
} from '../lib/buildingValidation'
import AddressAutocomplete from './ui/address-autocomplete'
import { loadGoogleMapsAPI } from '../lib/googleMapsLoader'

const BuildingManagement = () => {
  const { user } = useAuth()
  const {
    buildings,
    loading,
    error,
    createBuilding,
    updateBuilding,
    deleteBuilding,
    toggleBuildingStatus,
    generateUniqueEmail,
    isEmailUnique,
    searchBuildings
  } = useBuildings()

  const [searchTerm, setSearchTerm] = useState('')
  const [showForm, setShowForm] = useState(false)
  const [editingBuilding, setEditingBuilding] = useState(null)
  const [formData, setFormData] = useState({
    name: '',
    address: '',
    email_address: '',
    building_code: '',
    contact_phone: '',
    contact_email: '',
    is_active: true
  })
  const [googleMapsLoaded, setGoogleMapsLoaded] = useState(false)
  const [formErrors, setFormErrors] = useState({})
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [notification, setNotification] = useState(null)
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(null)
  const [isGeneratingEmail, setIsGeneratingEmail] = useState(false)

  // Filter buildings based on search term
  const filteredBuildings = searchBuildings(searchTerm)

  // Auto-save form data
  useEffect(() => {
    if (showForm && (formData.name || formData.address)) {
      const formId = editingBuilding ? editingBuilding.id : 'new'
      autoSaveFormData(formId, formData)
    }
  }, [formData, showForm, editingBuilding])

  // Load auto-saved data when opening form
  useEffect(() => {
    if (showForm && !editingBuilding) {
      const savedData = loadAutoSavedFormData('new')
      if (savedData) {
        setFormData(savedData)
        showNotification('Draft data restored', 'info')
      }
    }
  }, [showForm, editingBuilding])

  // Load Google Maps API
  useEffect(() => {
    loadGoogleMapsAPI()
      .then(() => setGoogleMapsLoaded(true))
      .catch(error => {
        console.error('Failed to load Google Maps:', error)
        // Continue without autocomplete functionality
        setGoogleMapsLoaded(false)
      })
  }, [])

  const showNotification = (message, type = 'success') => {
    setNotification({ message, type })
    setTimeout(() => setNotification(null), 5000)
  }

  const resetForm = () => {
    setFormData({
      name: '',
      address: '',
      email_address: '',
      building_code: '',
      contact_phone: '',
      contact_email: '',
      is_active: true
    })
    setFormErrors({})
    setEditingBuilding(null)
    clearAutoSavedFormData(editingBuilding ? editingBuilding.id : 'new')
  }

  const handleOpenForm = (building = null) => {
    if (building) {
      setEditingBuilding(building)
      setFormData({
        name: building.name || '',
        address: building.address || '',
        email_address: building.email_address || '',
        building_code: building.building_code || '',
        contact_phone: building.contact_phone || '',
        contact_email: building.contact_email || '',
        is_active: building.is_active
      })
    } else {
      resetForm()
    }
    setShowForm(true)
  }

  const handleCloseForm = () => {
    setShowForm(false)
    resetForm()
  }

  const handleInputChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    
    // Clear error for this field
    if (formErrors[field]) {
      setFormErrors(prev => ({ ...prev, [field]: null }))
    }

    // Auto-generate building code from name
    if (field === 'name' && !editingBuilding && !formData.building_code) {
      const suggestion = generateBuildingCodeSuggestion(value)
      setFormData(prev => ({ ...prev, building_code: suggestion }))
    }
  }

  const handleGenerateEmail = async () => {
    setIsGeneratingEmail(true)
    try {
      const email = await generateUniqueEmail()
      setFormData(prev => ({ ...prev, email_address: email }))
      showNotification('Unique email generated successfully', 'success')
    } catch (error) {
      showNotification('Failed to generate email: ' + error.message, 'error')
    } finally {
      setIsGeneratingEmail(false)
    }
  }

  const validateForm = async () => {
    const cleanedData = cleanBuildingFormData(formData)
    const validation = validateBuildingForm(cleanedData, !!editingBuilding)
    
    // Check email uniqueness
    if (validation.isValid && cleanedData.email_address) {
      const isUnique = await isEmailUnique(
        cleanedData.email_address, 
        editingBuilding ? editingBuilding.id : null
      )
      if (!isUnique) {
        validation.isValid = false
        validation.errors.email_address = 'This email address is already in use'
      }
    }

    setFormErrors(validation.errors)
    return validation.isValid
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    
    if (!user) {
      showNotification('You must be logged in to manage buildings', 'error')
      return
    }

    setIsSubmitting(true)
    
    try {
      const isValid = await validateForm()
      if (!isValid) {
        setIsSubmitting(false)
        return
      }

      const cleanedData = cleanBuildingFormData(formData)
      let result

      if (editingBuilding) {
        result = await updateBuilding(editingBuilding.id, cleanedData)
      } else {
        result = await createBuilding(cleanedData)
      }

      if (result.success) {
        showNotification(
          `Building ${editingBuilding ? 'updated' : 'created'} successfully`,
          'success'
        )
        handleCloseForm()
      } else {
        showNotification(result.error, 'error')
      }
    } catch {
      showNotification('An unexpected error occurred', 'error')
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleDelete = async (buildingId) => {
    if (!user) {
      showNotification('You must be logged in to delete buildings', 'error')
      return
    }

    try {
      const result = await deleteBuilding(buildingId)
      if (result.success) {
        showNotification('Building deleted successfully', 'success')
        setShowDeleteConfirm(null)
      } else {
        showNotification(result.error, 'error')
      }
    } catch {
      showNotification('Failed to delete building', 'error')
    }
  }

  const handleToggleStatus = async (buildingId, currentStatus) => {
    if (!user) {
      showNotification('You must be logged in to modify buildings', 'error')
      return
    }

    try {
      const result = await toggleBuildingStatus(buildingId, !currentStatus)
      if (result.success) {
        showNotification(
          `Building ${!currentStatus ? 'activated' : 'deactivated'} successfully`,
          'success'
        )
      } else {
        showNotification(result.error, 'error')
      }
    } catch {
      showNotification('Failed to update building status', 'error')
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading buildings...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-4 m-4">
        <h3 className="text-red-800 font-medium">Error Loading Buildings</h3>
        <p className="text-red-600 mt-1">{error}</p>
      </div>
    )
  }

  return (
    <div className="p-3 sm:p-4 max-w-full mx-auto">
      {/* Notification */}
      {notification && (
        <div className={`fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg ${
          notification.type === 'success' ? 'bg-green-100 text-green-800 border border-green-200' :
          notification.type === 'error' ? 'bg-red-100 text-red-800 border border-red-200' :
          'bg-blue-100 text-blue-800 border border-blue-200'
        }`}>
          <div className="flex items-center">
            <span>{notification.message}</span>
            <button
              onClick={() => setNotification(null)}
              className="ml-4 text-gray-500 hover:text-gray-700"
            >
              ×
            </button>
          </div>
        </div>
      )}

      {/* Header */}
      <div className="mb-6 flex flex-col sm:flex-row sm:justify-between sm:items-start gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Building Management</h1>
          <p className="text-gray-600">Manage building information and alarm email addresses</p>
        </div>

        <button
          onClick={() => handleOpenForm()}
          className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
        >
          Add New Building
        </button>
      </div>

      {/* Search and Stats */}
      <div className="mb-6 flex flex-col sm:flex-row gap-4">
        <div className="flex-1">
          <div className="relative">
            <input
              type="text"
              placeholder="Search buildings by name, code, or address..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
            <svg className="absolute left-3 top-2.5 h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          </div>
        </div>

        <div className="flex gap-4 text-sm">
          <div className="bg-white rounded-lg shadow p-3 border-l-4 border-blue-500">
            <span className="text-gray-500">Total Buildings:</span>
            <span className="ml-2 font-bold text-gray-900">{buildings.length}</span>
          </div>
          <div className="bg-white rounded-lg shadow p-3 border-l-4 border-green-500">
            <span className="text-gray-500">Active:</span>
            <span className="ml-2 font-bold text-green-600">
              {buildings.filter(b => b.is_active).length}
            </span>
          </div>
          <div className="bg-white rounded-lg shadow p-3 border-l-4 border-gray-500">
            <span className="text-gray-500">Inactive:</span>
            <span className="ml-2 font-bold text-gray-600">
              {buildings.filter(b => !b.is_active).length}
            </span>
          </div>
        </div>
      </div>

      {/* Buildings Table */}
      <div className="bg-white rounded-lg shadow overflow-hidden">
        <div className="px-4 py-3 border-b border-gray-200">
          <h2 className="text-lg font-medium text-gray-900">
            Buildings ({filteredBuildings.length})
          </h2>
        </div>

        {filteredBuildings.length === 0 ? (
          <div className="p-8 text-center text-gray-500">
            <p>No buildings found matching the search criteria.</p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Building
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Address
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Email Address
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Contact
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredBuildings.map(building => (
                  <tr key={building.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div>
                        <div className="text-sm font-medium text-gray-900">{building.name}</div>
                        {building.building_code && (
                          <div className="text-sm text-gray-500">Code: {building.building_code}</div>
                        )}
                      </div>
                    </td>

                    <td className="px-6 py-4 text-sm text-gray-900 max-w-xs">
                      <div className="truncate" title={building.address}>
                        {building.address}
                      </div>
                    </td>

                    <td className="px-6 py-4 text-sm text-gray-900">
                      <div className="font-mono text-xs bg-gray-100 px-2 py-1 rounded">
                        {building.email_address}
                      </div>
                    </td>

                    <td className="px-6 py-4 text-sm text-gray-900">
                      <div>
                        {building.contact_phone && (
                          <div>{formatPhoneNumber(building.contact_phone)}</div>
                        )}
                        {building.contact_email && (
                          <div className="text-gray-500">{building.contact_email}</div>
                        )}
                      </div>
                    </td>

                    <td className="px-6 py-4 whitespace-nowrap">
                      <button
                        onClick={() => handleToggleStatus(building.id, building.is_active)}
                        className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                          building.is_active
                            ? 'bg-green-100 text-green-800 hover:bg-green-200'
                            : 'bg-gray-100 text-gray-800 hover:bg-gray-200'
                        }`}
                      >
                        {building.is_active ? 'Active' : 'Inactive'}
                      </button>
                    </td>

                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <div className="flex justify-end gap-2">
                        <button
                          onClick={() => handleOpenForm(building)}
                          className="text-blue-600 hover:text-blue-900 text-xs px-2 py-1 border border-blue-300 rounded hover:bg-blue-50"
                        >
                          Edit
                        </button>

                        <button
                          onClick={() => setShowDeleteConfirm(building.id)}
                          className="text-red-600 hover:text-red-900 text-xs px-2 py-1 border border-red-300 rounded hover:bg-red-50"
                        >
                          Delete
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* Building Form Modal */}
      {showForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <div className="px-6 py-4 border-b border-gray-200">
              <h3 className="text-lg font-medium text-gray-900">
                {editingBuilding ? 'Edit Building' : 'Add New Building'}
              </h3>
            </div>

            <form onSubmit={handleSubmit} className="px-6 py-4 space-y-4">
              {/* Building Name */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Building Name *
                </label>
                <input
                  type="text"
                  value={formData.name}
                  onChange={(e) => handleInputChange('name', e.target.value)}
                  className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                    formErrors.name ? 'border-red-300' : 'border-gray-300'
                  }`}
                  placeholder="Enter building name"
                />
                {formErrors.name && (
                  <p className="mt-1 text-sm text-red-600">{formErrors.name}</p>
                )}
              </div>

              {/* Address */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Address *
                </label>
                {googleMapsLoaded ? (
                  <AddressAutocomplete
                    value={formData.address}
                    onChange={(value) => handleInputChange('address', value)}
                    onSelect={(address, suggestion) => {
                      handleInputChange('address', address)
                      // You can store additional data from suggestion if needed
                      console.log('Selected address:', { address, suggestion })
                    }}
                    onCoordinatesSelect={({ lat, lng, address }) => {
                      // Store coordinates if needed for future features
                      console.log('Address coordinates:', { lat, lng, address })
                    }}
                    placeholder="Enter building address"
                    error={formErrors.address}
                    className={formErrors.address ? 'border-red-300' : ''}
                  />
                ) : (
                  <textarea
                    value={formData.address}
                    onChange={(e) => handleInputChange('address', e.target.value)}
                    rows={3}
                    className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                      formErrors.address ? 'border-red-300' : 'border-gray-300'
                    }`}
                    placeholder="Enter building address"
                  />
                )}
                {formErrors.address && (
                  <p className="mt-1 text-sm text-red-600">{formErrors.address}</p>
                )}
              </div>

              {/* Email Address */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Alarm Email Address *
                </label>
                <div className="flex gap-2">
                  <input
                    type="email"
                    value={formData.email_address}
                    onChange={(e) => handleInputChange('email_address', e.target.value)}
                    className={`flex-1 px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                      formErrors.email_address ? 'border-red-300' : 'border-gray-300'
                    }`}
                    placeholder="<EMAIL>"
                  />
                  <button
                    type="button"
                    onClick={handleGenerateEmail}
                    disabled={isGeneratingEmail}
                    className="px-3 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 disabled:opacity-50"
                  >
                    {isGeneratingEmail ? '...' : 'Generate'}
                  </button>
                </div>
                {formErrors.email_address && (
                  <p className="mt-1 text-sm text-red-600">{formErrors.email_address}</p>
                )}
                <p className="mt-1 text-xs text-gray-500">
                  This email address will receive alarm notifications for this building
                </p>
              </div>

              {/* Building Code */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Building Code
                </label>
                <input
                  type="text"
                  value={formData.building_code}
                  onChange={(e) => handleInputChange('building_code', e.target.value)}
                  className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                    formErrors.building_code ? 'border-red-300' : 'border-gray-300'
                  }`}
                  placeholder="e.g., MOB-001"
                />
                {formErrors.building_code && (
                  <p className="mt-1 text-sm text-red-600">{formErrors.building_code}</p>
                )}
              </div>

              {/* Contact Information */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Contact Phone
                  </label>
                  <input
                    type="tel"
                    value={formData.contact_phone}
                    onChange={(e) => handleInputChange('contact_phone', e.target.value)}
                    className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                      formErrors.contact_phone ? 'border-red-300' : 'border-gray-300'
                    }`}
                    placeholder="(*************"
                  />
                  {formErrors.contact_phone && (
                    <p className="mt-1 text-sm text-red-600">{formErrors.contact_phone}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Contact Email
                  </label>
                  <input
                    type="email"
                    value={formData.contact_email}
                    onChange={(e) => handleInputChange('contact_email', e.target.value)}
                    className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                      formErrors.contact_email ? 'border-red-300' : 'border-gray-300'
                    }`}
                    placeholder="<EMAIL>"
                  />
                  {formErrors.contact_email && (
                    <p className="mt-1 text-sm text-red-600">{formErrors.contact_email}</p>
                  )}
                </div>
              </div>

              {/* Active Status */}
              <div>
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={formData.is_active}
                    onChange={(e) => handleInputChange('is_active', e.target.checked)}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <span className="ml-2 text-sm text-gray-700">Building is active</span>
                </label>
              </div>

              {/* Form Actions */}
              <div className="flex justify-end gap-3 pt-4 border-t border-gray-200">
                <button
                  type="button"
                  onClick={handleCloseForm}
                  className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={isSubmitting}
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
                >
                  {isSubmitting ? 'Saving...' : editingBuilding ? 'Update Building' : 'Create Building'}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Delete Confirmation Dialog */}
      {showDeleteConfirm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-md w-full">
            <div className="px-6 py-4">
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                Confirm Delete
              </h3>
              <p className="text-gray-600 mb-6">
                Are you sure you want to delete this building? This action cannot be undone.
                All associated alarm notifications will remain but will no longer be linked to this building.
              </p>
              <div className="flex justify-end gap-3">
                <button
                  onClick={() => setShowDeleteConfirm(null)}
                  className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200"
                >
                  Cancel
                </button>
                <button
                  onClick={() => handleDelete(showDeleteConfirm)}
                  className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700"
                >
                  Delete Building
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default BuildingManagement
