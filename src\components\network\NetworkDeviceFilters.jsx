import React from 'react'
import { DEVICE_TYPES } from '../../lib/networkDeviceValidation'

/**
 * NetworkDeviceFilters component for search and filter functionality
 */
const NetworkDeviceFilters = ({
  searchTerm,
  selectedBuilding,
  selectedDeviceType,
  buildings,
  onSearchChange,
  onBuildingChange,
  onDeviceTypeChange
}) => {
  return (
    <div className="mb-6 space-y-4">
      {/* Header */}
      <div className="flex items-center">
        <h2 className="text-lg font-semibold text-gray-900">Network Devices</h2>
      </div>

      {/* Filters */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {/* Search */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">Search</label>
          <input
            type="text"
            placeholder="Search devices..."
            value={searchTerm}
            onChange={(e) => onSearchChange(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>

        {/* Building Filter */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">Building</label>
          <select
            value={selectedBuilding}
            onChange={(e) => onBuildingChange(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="">All Buildings</option>
            {buildings.map(building => (
              <option key={building.id} value={building.id}>
                {building.name} {building.building_code ? `(${building.building_code})` : ''}
              </option>
            ))}
          </select>
        </div>

        {/* Device Type Filter */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">Device Type</label>
          <select
            value={selectedDeviceType}
            onChange={(e) => onDeviceTypeChange(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="">All Types</option>
            {DEVICE_TYPES.map(type => (
              <option key={type} value={type}>
                {type}
              </option>
            ))}
          </select>
        </div>
      </div>
    </div>
  )
}

export default NetworkDeviceFilters
