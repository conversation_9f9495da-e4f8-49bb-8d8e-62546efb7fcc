import React from 'react'
import <PERSON><PERSON><PERSON>utton from '../ui/CopyButton'
import NetworkCredentialCell from './NetworkCredentialCell'

/**
 * NetworkDeviceTable component for displaying devices in a spreadsheet-style table
 */
const NetworkDeviceTable = ({
  devices,
  onEdit,
  onToggleStatus,
  onDelete,
  showNotification
}) => {
  // Use devices directly since pagination is handled by parent component
  const currentPageDevices = devices

  return (
    <div className="bg-white shadow-lg rounded-lg overflow-hidden border-2 border-gray-300 mb-6">
      {/* Table Header with Results Count */}
      <div className="px-4 py-3 border-b border-gray-200 bg-gray-50 flex justify-between items-center">
        <div className="text-sm text-gray-700">
          Showing {devices.length} device{devices.length !== 1 ? 's' : ''}
        </div>
      </div>

      {/* Table Container with Fixed Height */}
      <div className="overflow-auto max-h-[600px] relative">
        <table className="min-w-full border-collapse table-fixed">
          <thead className="bg-gradient-to-b from-gray-50 to-gray-100 border-b-2 border-gray-400 sticky top-0 z-10">
            <tr>
              <th className="px-2 py-3 text-left text-xs font-bold text-gray-800 uppercase tracking-wide border-r-2 border-gray-300 w-36 bg-gray-100">
                <div className="truncate">Station Name</div>
              </th>
              <th className="px-2 py-3 text-left text-xs font-bold text-gray-800 uppercase tracking-wide border-r-2 border-gray-300 w-28 bg-gray-100">
                <div className="truncate">Device Type</div>
              </th>
              <th className="px-2 py-3 text-left text-xs font-bold text-gray-800 uppercase tracking-wide border-r-2 border-gray-300 w-24 bg-gray-100">
                <div className="truncate">Host ID</div>
              </th>
              <th className="px-2 py-3 text-left text-xs font-bold text-gray-800 uppercase tracking-wide border-r-2 border-gray-300 w-32 bg-gray-100">
                <div className="truncate">IP Address</div>
              </th>
              <th className="px-2 py-3 text-left text-xs font-bold text-gray-800 uppercase tracking-wide border-r-2 border-gray-300 w-32 bg-gray-100">
                <div className="truncate">Subnet Mask</div>
              </th>
              <th className="px-2 py-3 text-left text-xs font-bold text-gray-800 uppercase tracking-wide border-r-2 border-gray-300 w-32 bg-gray-100">
                <div className="truncate">Gateway</div>
              </th>
              <th className="px-2 py-3 text-left text-xs font-bold text-gray-800 uppercase tracking-wide border-r-2 border-gray-300 w-32 bg-gray-100">
                <div className="truncate">Building</div>
              </th>
              <th className="px-2 py-3 text-left text-xs font-bold text-gray-800 uppercase tracking-wide border-r-2 border-gray-300 w-28 bg-gray-100">
                <div className="truncate">Station User</div>
              </th>
              <th className="px-2 py-3 text-left text-xs font-bold text-gray-800 uppercase tracking-wide border-r-2 border-gray-300 w-28 bg-gray-100">
                <div className="truncate">Windows User</div>
              </th>
              <th className="px-2 py-3 text-left text-xs font-bold text-gray-800 uppercase tracking-wide border-r-2 border-gray-300 w-28 bg-gray-100">
                <div className="truncate">Platform User</div>
              </th>
              <th className="px-2 py-3 text-left text-xs font-bold text-gray-800 uppercase tracking-wide border-r-2 border-gray-300 w-24 bg-gray-100">
                <div className="truncate">Version</div>
              </th>
              <th className="px-2 py-3 text-left text-xs font-bold text-gray-800 uppercase tracking-wide border-r-2 border-gray-300 w-20 bg-gray-100">
                <div className="truncate">Status</div>
              </th>
              <th className="px-2 py-3 text-center text-xs font-bold text-gray-800 uppercase tracking-wide w-32 bg-gray-100">
                <div className="truncate">Actions</div>
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y-2 divide-gray-300">
            {currentPageDevices.map((device, index) => (
              <tr key={device.id} className={`${index % 2 === 0 ? 'bg-white' : 'bg-slate-50'} hover:bg-blue-50 transition-colors border-b border-gray-300`}>
                <td className="px-2 py-1.5 text-sm text-gray-900 border-r-2 border-gray-300 font-medium truncate" title={device.station_name || '-'}>
                  {device.station_name || '-'}
                </td>

                <td className="px-2 py-1.5 text-sm text-gray-700 border-r-2 border-gray-300 truncate" title={device.device_type || '-'}>
                  {device.device_type || '-'}
                </td>

                <td className="px-2 py-1.5 text-sm text-gray-700 border-r-2 border-gray-300 font-mono truncate" title={device.host_id || '-'}>
                  {device.host_id || '-'}
                </td>

                <td className="px-2 py-1.5 text-sm text-gray-900 border-r-2 border-gray-300 font-mono">
                  <div className="flex items-center space-x-1">
                    <span className="truncate" title={device.ip_address}>{device.ip_address || '-'}</span>
                    {device.ip_address && (
                      <CopyButton
                        text={device.ip_address}
                        label="IP Address"
                        onSuccess={(msg) => showNotification(msg, 'success')}
                        onError={(msg) => showNotification(msg, 'error')}
                        size="xs"
                        variant="ghost"
                      />
                    )}
                  </div>
                </td>

                <td className="px-2 py-1.5 text-sm text-gray-700 border-r-2 border-gray-300 font-mono truncate" title={device.subnet_mask || '-'}>
                  {device.subnet_mask || '-'}
                </td>

                <td className="px-2 py-1.5 text-sm text-gray-700 border-r-2 border-gray-300 font-mono">
                  <div className="flex items-center space-x-1">
                    <span className="truncate" title={device.gateway}>{device.gateway || '-'}</span>
                    {device.gateway && (
                      <CopyButton
                        text={device.gateway}
                        label="Gateway"
                        onSuccess={(msg) => showNotification(msg, 'success')}
                        onError={(msg) => showNotification(msg, 'error')}
                        size="xs"
                        variant="ghost"
                      />
                    )}
                  </div>
                </td>

                <td className="px-2 py-1.5 text-sm text-gray-700 border-r-2 border-gray-300 truncate">
                  <div title={`${device.building?.name || '-'}${device.building?.building_code ? ` (${device.building.building_code})` : ''}`}>
                    <div className="font-medium truncate">{device.building?.name || '-'}</div>
                    {device.building?.building_code && (
                      <div className="text-xs text-gray-500 truncate">{device.building.building_code}</div>
                    )}
                  </div>
                </td>
                
                <td className="px-2 py-1 text-sm text-gray-700 border-r-2 border-gray-300">
                  <NetworkCredentialCell
                    username={device.station_username}
                    device={device}
                    passwordField="station_password_encrypted"
                    showNotification={showNotification}
                  />
                </td>

                <td className="px-2 py-1 text-sm text-gray-700 border-r-2 border-gray-300">
                  <NetworkCredentialCell
                    username={device.windows_username}
                    device={device}
                    passwordField="windows_password_encrypted"
                    showNotification={showNotification}
                  />
                </td>

                <td className="px-2 py-1 text-sm text-gray-700 border-r-2 border-gray-300">
                  <NetworkCredentialCell
                    username={device.platform_username}
                    device={device}
                    passwordField="platform_password_encrypted"
                    showNotification={showNotification}
                  />
                </td>

                <td className="px-2 py-1.5 text-sm text-gray-700 border-r-2 border-gray-300 truncate" title={device.software_version || '-'}>
                  {device.software_version || '-'}
                </td>

                <td className="px-2 py-1.5 border-r-2 border-gray-300">
                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                    device.is_active
                      ? 'bg-green-100 text-green-800 border border-green-200'
                      : 'bg-red-100 text-red-800 border border-red-200'
                  }`}>
                    {device.is_active ? 'Active' : 'Inactive'}
                  </span>
                </td>

                <td className="px-2 py-1.5 text-center bg-gray-50">
                  <div className="flex justify-center space-x-1">
                    <button
                      onClick={() => onEdit(device)}
                      className="p-1.5 text-blue-600 hover:text-blue-800 hover:bg-blue-100 rounded transition-colors border border-transparent hover:border-blue-200"
                      title="Edit Device"
                    >
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                      </svg>
                    </button>
                    <button
                      onClick={() => onToggleStatus(device.id, device.is_active)}
                      className={`p-1.5 rounded transition-colors border border-transparent ${
                        device.is_active 
                          ? 'text-orange-600 hover:text-orange-800 hover:bg-orange-100 hover:border-orange-200' 
                          : 'text-green-600 hover:text-green-800 hover:bg-green-100 hover:border-green-200'
                      }`}
                      title={device.is_active ? 'Deactivate Device' : 'Activate Device'}
                    >
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        {device.is_active ? (
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L5.636 5.636" />
                        ) : (
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                        )}
                      </svg>
                    </button>
                    <button
                      onClick={() => onDelete(device)}
                      className="p-1.5 text-red-600 hover:text-red-800 hover:bg-red-100 rounded transition-colors border border-transparent hover:border-red-200"
                      title="Delete Device"
                    >
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                      </svg>
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  )
}

export default NetworkDeviceTable
