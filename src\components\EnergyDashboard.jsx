import React, { useState, useEffect, useCallback, useMemo } from 'react'
import { supabase } from '../lib/supabase'
import { useAuth } from '../contexts/AuthContext'
import { useViewMode } from '../hooks/useViewMode'

const EnergyDashboard = () => {
  const { user: _user } = useAuth()
  const { viewMode, setViewMode } = useViewMode('energy', 'summary')
  const [energyData, setEnergyData] = useState([])
  const [buildings, setBuildings] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)
  const [selectedBuilding, setSelectedBuilding] = useState('all')
  const [selectedEnergyType, setSelectedEnergyType] = useState('all')
  const [timeRange, setTimeRange] = useState('24h')

  const energyTypes = ['electricity', 'gas', 'water', 'steam', 'chilled_water']
  const timeRanges = useMemo(() => ({
    '24h': { label: '24 Hours', hours: 24 },
    '7d': { label: '7 Days', hours: 168 },
    '30d': { label: '30 Days', hours: 720 },
    '90d': { label: '90 Days', hours: 2160 }
  }), [])

  const fetchData = useCallback(async () => {
    try {
      setLoading(true)
      
      // Fetch buildings
      const { data: buildingsData, error: buildingsError } = await supabase
        .from('buildings')
        .select('*')
        .eq('is_active', true)
        .order('name')

      if (buildingsError) throw buildingsError
      setBuildings(buildingsData || [])

      // Calculate time range
      const hoursAgo = timeRanges[timeRange].hours
      const startTime = new Date(Date.now() - hoursAgo * 60 * 60 * 1000).toISOString()

      // Build query for energy data
      let query = supabase
        .from('energy_data')
        .select(`
          *,
          building:buildings(name, building_code),
          equipment:equipment(category, equipment_type)
        `)
        .gte('timestamp', startTime)
        .order('timestamp', { ascending: false })

      if (selectedBuilding !== 'all') {
        query = query.eq('building_id', selectedBuilding)
      }

      if (selectedEnergyType !== 'all') {
        query = query.eq('energy_type', selectedEnergyType)
      }

      const { data: energyDataResult, error: energyError } = await query

      if (energyError) throw energyError
      setEnergyData(energyDataResult || [])

    } catch (err) {
      console.error('Error fetching energy data:', err)
      setError(err.message)
    } finally {
      setLoading(false)
    }
  }, [timeRange, selectedBuilding, selectedEnergyType, timeRanges])

  useEffect(() => {
    fetchData()
  }, [fetchData])

  // Calculate summary statistics
  const calculateSummary = () => {
    const summary = {}
    
    energyTypes.forEach(type => {
      const typeData = energyData.filter(d => d.energy_type === type)
      const totalConsumption = typeData.reduce((sum, d) => sum + (d.consumption_value || 0), 0)
      const totalCost = typeData.reduce((sum, d) => sum + (d.cost || 0), 0)
      const avgDemand = typeData.length > 0 ? 
        typeData.reduce((sum, d) => sum + (d.demand_value || 0), 0) / typeData.length : 0

      summary[type] = {
        consumption: totalConsumption,
        cost: totalCost,
        avgDemand: avgDemand,
        unit: typeData[0]?.consumption_unit || '',
        demandUnit: typeData[0]?.demand_unit || ''
      }
    })

    return summary
  }

  const summary = calculateSummary()

  const getEnergyTypeIcon = (type) => {
    switch (type) {
      case 'electricity': return '⚡'
      case 'gas': return '🔥'
      case 'water': return '💧'
      case 'steam': return '♨️'
      case 'chilled_water': return '❄️'
      default: return '📊'
    }
  }

  const getEnergyTypeColor = (type) => {
    switch (type) {
      case 'electricity': return '#f59e0b'
      case 'gas': return '#ef4444'
      case 'water': return '#3b82f6'
      case 'steam': return '#8b5cf6'
      case 'chilled_water': return '#06b6d4'
      default: return '#6b7280'
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading energy data...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-4 m-4">
        <h3 className="text-red-800 font-medium">Error Loading Energy Data</h3>
        <p className="text-red-600 mt-1">{error}</p>
      </div>
    )
  }

  return (
    <div className="p-3 sm:p-4 max-w-full mx-auto">
      {/* Header */}
      <div className="mb-6 flex flex-col sm:flex-row sm:justify-between sm:items-start gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Energy Management</h1>
          <p className="text-gray-600">Monitor energy consumption, costs, and efficiency</p>
        </div>

        <div className="flex flex-col sm:flex-row items-start sm:items-center gap-3">
          {/* View Toggle */}
          <div className="flex items-center bg-gray-100 rounded-lg p-1">
            <button
              onClick={() => setViewMode('summary')}
              className={`px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                viewMode === 'summary'
                  ? 'bg-white text-gray-900 shadow-sm'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              Summary
            </button>

            <button
              onClick={() => setViewMode('detailed')}
              className={`px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                viewMode === 'detailed'
                  ? 'bg-white text-gray-900 shadow-sm'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              Detailed
            </button>
          </div>
        </div>
      </div>

      {/* Energy Type Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4 mb-6">
        {energyTypes.map(type => {
          const data = summary[type]
          return (
            <div key={type} className="bg-white rounded-lg shadow p-4 border-l-4" 
                 style={{ borderLeftColor: getEnergyTypeColor(type) }}>
              <div className="flex items-center justify-between mb-2">
                <span className="text-2xl">{getEnergyTypeIcon(type)}</span>
                <span className="text-xs font-medium text-gray-500 uppercase">
                  {type.replace('_', ' ')}
                </span>
              </div>
              <div className="space-y-1">
                <div>
                  <p className="text-lg font-bold text-gray-900">
                    {data.consumption.toLocaleString()} {data.unit}
                  </p>
                  <p className="text-xs text-gray-500">Total Consumption</p>
                </div>
                {data.cost > 0 && (
                  <div>
                    <p className="text-sm font-medium text-green-600">
                      ${data.cost.toLocaleString()}
                    </p>
                    <p className="text-xs text-gray-500">Total Cost</p>
                  </div>
                )}
              </div>
            </div>
          )
        })}
      </div>

      {/* Filters */}
      <div className="bg-white rounded-lg shadow p-4 mb-6">
        <div className="flex flex-wrap gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Time Range</label>
            <select
              value={timeRange}
              onChange={(e) => setTimeRange(e.target.value)}
              className="border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              {Object.entries(timeRanges).map(([key, range]) => (
                <option key={key} value={key}>{range.label}</option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Building</label>
            <select
              value={selectedBuilding}
              onChange={(e) => setSelectedBuilding(e.target.value)}
              className="border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="all">All Buildings</option>
              {buildings.map(building => (
                <option key={building.id} value={building.id}>
                  {building.name} ({building.building_code})
                </option>
              ))}
            </select>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Energy Type</label>
            <select
              value={selectedEnergyType}
              onChange={(e) => setSelectedEnergyType(e.target.value)}
              className="border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="all">All Types</option>
              {energyTypes.map(type => (
                <option key={type} value={type}>
                  {getEnergyTypeIcon(type)} {type.replace('_', ' ').toUpperCase()}
                </option>
              ))}
            </select>
          </div>
        </div>
      </div>

      {/* Energy Data Display */}
      {viewMode === 'summary' ? (
        <EnergySummaryView energyData={energyData} getEnergyTypeIcon={getEnergyTypeIcon} />
      ) : (
        <EnergyDetailedView energyData={energyData} getEnergyTypeIcon={getEnergyTypeIcon} />
      )}
    </div>
  )
}

// Energy Summary View Component
const EnergySummaryView = ({ energyData, getEnergyTypeIcon }) => {
  // Group data by building and energy type
  const groupedData = energyData.reduce((acc, item) => {
    const buildingKey = item.building?.name || 'Unknown Building'
    if (!acc[buildingKey]) {
      acc[buildingKey] = {}
    }

    const energyType = item.energy_type
    if (!acc[buildingKey][energyType]) {
      acc[buildingKey][energyType] = {
        consumption: 0,
        cost: 0,
        count: 0,
        unit: item.consumption_unit
      }
    }

    acc[buildingKey][energyType].consumption += item.consumption_value || 0
    acc[buildingKey][energyType].cost += item.cost || 0
    acc[buildingKey][energyType].count += 1

    return acc
  }, {})

  return (
    <div className="bg-white rounded-lg shadow overflow-hidden">
      <div className="px-4 py-3 border-b border-gray-200">
        <h2 className="text-lg font-medium text-gray-900">Energy Summary by Building</h2>
      </div>

      {Object.keys(groupedData).length === 0 ? (
        <div className="p-8 text-center text-gray-500">
          <p>No energy data available for the selected criteria.</p>
        </div>
      ) : (
        <div className="divide-y divide-gray-200">
          {Object.entries(groupedData).map(([building, energyTypes]) => (
            <div key={building} className="p-4">
              <h3 className="font-medium text-gray-900 mb-3">{building}</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-3">
                {Object.entries(energyTypes).map(([type, data]) => (
                  <div key={type} className="bg-gray-50 rounded-lg p-3">
                    <div className="flex items-center gap-2 mb-2">
                      <span>{getEnergyTypeIcon(type)}</span>
                      <span className="text-sm font-medium text-gray-700">
                        {type.replace('_', ' ').toUpperCase()}
                      </span>
                    </div>
                    <div className="space-y-1">
                      <div>
                        <p className="text-lg font-bold text-gray-900">
                          {data.consumption.toLocaleString()} {data.unit}
                        </p>
                        <p className="text-xs text-gray-500">Consumption</p>
                      </div>
                      {data.cost > 0 && (
                        <div>
                          <p className="text-sm font-medium text-green-600">
                            ${data.cost.toLocaleString()}
                          </p>
                          <p className="text-xs text-gray-500">Cost</p>
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  )
}

// Energy Detailed View Component
const EnergyDetailedView = ({ energyData, getEnergyTypeIcon }) => {
  return (
    <div className="bg-white rounded-lg shadow overflow-hidden">
      <div className="px-4 py-3 border-b border-gray-200">
        <h2 className="text-lg font-medium text-gray-900">
          Detailed Energy Data ({energyData.length} records)
        </h2>
      </div>

      {energyData.length === 0 ? (
        <div className="p-8 text-center text-gray-500">
          <p>No energy data available for the selected criteria.</p>
        </div>
      ) : (
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Timestamp
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Building
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Energy Type
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Consumption
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Demand
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Cost
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Equipment
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {energyData.map(item => (
                <tr key={item.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    <div>{new Date(item.timestamp).toLocaleDateString()}</div>
                    <div className="text-gray-500 text-xs">
                      {new Date(item.timestamp).toLocaleTimeString()}
                    </div>
                  </td>

                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    <div>{item.building?.name || 'Unknown'}</div>
                    <div className="text-gray-500 text-xs">{item.building?.building_code}</div>
                  </td>

                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    <div className="flex items-center gap-2">
                      <span>{getEnergyTypeIcon(item.energy_type)}</span>
                      <span>{item.energy_type.replace('_', ' ').toUpperCase()}</span>
                    </div>
                  </td>

                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    <div className="font-medium">
                      {item.consumption_value?.toLocaleString()} {item.consumption_unit}
                    </div>
                  </td>

                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {item.demand_value ? (
                      <div>
                        {item.demand_value.toLocaleString()} {item.demand_unit}
                      </div>
                    ) : (
                      <span className="text-gray-400">N/A</span>
                    )}
                  </td>

                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {item.cost ? (
                      <div className="font-medium text-green-600">
                        ${item.cost.toLocaleString()}
                      </div>
                    ) : (
                      <span className="text-gray-400">N/A</span>
                    )}
                  </td>

                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {item.equipment ? (
                      <div>
                        <div>{item.equipment.category}</div>
                        <div className="text-gray-500 text-xs">{item.equipment.equipment_type}</div>
                      </div>
                    ) : (
                      <span className="text-gray-400">Building Level</span>
                    )}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
    </div>
  )
}

export default EnergyDashboard
