import React, { useState } from 'react'
import { useAlarms } from '../hooks/useAlarms'
import { useAuth } from '../contexts/AuthContext'
import { useViewMode } from '../hooks/useViewMode'
import { formatAlarmNotification } from '../lib/alarmUtils'
import type { AlarmNotification, AlarmStatus } from '@/types'

interface AlarmDashboardProps {
  className?: string
}

const AlarmDashboard: React.FC<AlarmDashboardProps> = ({ className }) => {
  const { user } = useAuth()
  const { viewMode, setViewMode } = useViewMode('alarms', 'card')
  const {
    alarms,
    loading,
    error,
    acknowledgeAlarm,
    resolveAlarm,
    getAlarmsByStatus,
    getAlarmsBySeverity,
    severityLevels
  } = useAlarms()

  const [selectedStatus, setSelectedStatus] = useState<string>('all')
  const [selectedSeverity, setSelectedSeverity] = useState<string>('all')
  const [processingAlarmId, setProcessingAlarmId] = useState<string | null>(null)

  // Filter alarms based on selected criteria
  const filteredAlarms = alarms.filter(alarm => {
    const statusMatch = selectedStatus === 'all' || alarm.status === selectedStatus
    const severityMatch = selectedSeverity === 'all' || 
      alarm.severity?.name?.toLowerCase() === selectedSeverity.toLowerCase()
    return statusMatch && severityMatch
  })

  const handleAcknowledgeAlarm = async (alarmId) => {
    if (!user) return
    
    setProcessingAlarmId(alarmId)
    try {
      const result = await acknowledgeAlarm(alarmId, user.id, 'Acknowledged via dashboard')
      if (!result.success) {
        console.error('Failed to acknowledge alarm:', result.error)
      }
    } finally {
      setProcessingAlarmId(null)
    }
  }

  const handleResolveAlarm = async (alarmId) => {
    if (!user) return

    setProcessingAlarmId(alarmId)
    try {
      const result = await resolveAlarm(alarmId, user.id, 'Resolved via dashboard')
      if (!result.success) {
        console.error('Failed to resolve alarm:', result.error)
      }
    } finally {
      setProcessingAlarmId(null)
    }
  }



  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading alarms...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-4 m-4">
        <h3 className="text-red-800 font-medium">Error Loading Alarms</h3>
        <p className="text-red-600 mt-1">{error}</p>
      </div>
    )
  }

  // Get alarm counts by status
  const statusCounts = {
    received: getAlarmsByStatus('received').length,
    acknowledged: getAlarmsByStatus('acknowledged').length,
    resolved: getAlarmsByStatus('resolved').length,
    total: alarms.length
  }

  // Get critical alarms count
  const criticalAlarms = getAlarmsBySeverity('CRITICAL').filter(alarm => 
    alarm.status !== 'resolved'
  ).length

  return (
    <div className="p-3 sm:p-4 max-w-full mx-auto">
      {/* Header */}
      <div className="mb-6 flex flex-col sm:flex-row sm:justify-between sm:items-start gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Alarm Dashboard</h1>
          <p className="text-gray-600">Monitor and manage building alarm notifications</p>
        </div>

        <div className="flex flex-col sm:flex-row items-start sm:items-center gap-3">
          {/* View Toggle */}
          <div className="flex items-center bg-gray-100 rounded-lg p-1">
            <button
              onClick={() => setViewMode('card')}
              className={`flex items-center gap-1 sm:gap-2 px-2 sm:px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                viewMode === 'card'
                  ? 'bg-white text-gray-900 shadow-sm'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" />
              </svg>
              <span className="hidden sm:inline">Cards</span>
            </button>

            <button
              onClick={() => setViewMode('table')}
              className={`flex items-center gap-1 sm:gap-2 px-2 sm:px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                viewMode === 'table'
                  ? 'bg-white text-gray-900 shadow-sm'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 10h16M4 14h16M4 18h16" />
              </svg>
              <span className="hidden sm:inline">Table</span>
            </button>
          </div>


        </div>
      </div>

      {/* Status Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <div className="bg-white rounded-lg shadow p-4 border-l-4 border-blue-500">
          <h3 className="text-sm font-medium text-gray-500">Total Alarms</h3>
          <p className="text-2xl font-bold text-gray-900">{statusCounts.total}</p>
        </div>
        
        <div className="bg-white rounded-lg shadow p-4 border-l-4 border-red-500">
          <h3 className="text-sm font-medium text-gray-500">Critical Active</h3>
          <p className="text-2xl font-bold text-red-600">{criticalAlarms}</p>
        </div>
        
        <div className="bg-white rounded-lg shadow p-4 border-l-4 border-yellow-500">
          <h3 className="text-sm font-medium text-gray-500">Acknowledged</h3>
          <p className="text-2xl font-bold text-yellow-600">{statusCounts.acknowledged}</p>
        </div>
        
        <div className="bg-white rounded-lg shadow p-4 border-l-4 border-green-500">
          <h3 className="text-sm font-medium text-gray-500">Resolved</h3>
          <p className="text-2xl font-bold text-green-600">{statusCounts.resolved}</p>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white rounded-lg shadow p-4 mb-6">
        <div className="flex flex-wrap gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Status</label>
            <select
              value={selectedStatus}
              onChange={(e) => setSelectedStatus(e.target.value)}
              className="border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="all">All Statuses</option>
              <option value="received">Received</option>
              <option value="acknowledged">Acknowledged</option>
              <option value="resolved">Resolved</option>
            </select>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Severity</label>
            <select
              value={selectedSeverity}
              onChange={(e) => setSelectedSeverity(e.target.value)}
              className="border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="all">All Severities</option>
              {severityLevels.map(level => (
                <option key={level.id} value={level.name}>{level.name}</option>
              ))}
            </select>
          </div>
        </div>
      </div>

      {/* Alarms List */}
      <div className="bg-white rounded-lg shadow overflow-hidden">
        <div className="px-4 py-3 border-b border-gray-200">
          <h2 className="text-lg font-medium text-gray-900">
            Alarm Notifications ({filteredAlarms.length})
          </h2>
        </div>

        {filteredAlarms.length === 0 ? (
          <div className="p-8 text-center text-gray-500">
            <p>No alarms found matching the selected criteria.</p>
          </div>
        ) : viewMode === 'card' ? (
          <div className="divide-y divide-gray-200">
            {filteredAlarms.map(alarm => {
              const formatted = formatAlarmNotification(alarm)
              return (
                <AlarmCard
                  key={alarm.id}
                  alarm={formatted}
                  onAcknowledge={() => handleAcknowledgeAlarm(alarm.id)}
                  onResolve={() => handleResolveAlarm(alarm.id)}
                  isProcessing={processingAlarmId === alarm.id}
                  canAcknowledge={user && alarm.status === 'received'}
                  canResolve={user && ['received', 'acknowledged'].includes(alarm.status)}
                />
              )
            })}
          </div>
        ) : (
          <AlarmTable
            alarms={filteredAlarms}
            onAcknowledge={handleAcknowledgeAlarm}
            onResolve={handleResolveAlarm}
            processingAlarmId={processingAlarmId}
            user={user}
          />
        )}
      </div>
    </div>
  )
}

// Alarm table component for compact view
const AlarmTable = ({ alarms, onAcknowledge, onResolve, processingAlarmId, user }) => {
  return (
    <div className="overflow-x-auto">
      <table className="w-full table-auto divide-y divide-gray-200">
        <thead className="bg-gray-50">
          <tr>
            <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-[15%]">
              Building
            </th>
            <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-[12%]">
              Type
            </th>
            <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-[10%]">
              Severity
            </th>
            <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-[15%]">
              Time
            </th>
            <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-[10%]">
              Status
            </th>
            <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-[25%]">
              Details
            </th>
            <th className="px-3 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider w-[13%]">
              Actions
            </th>
          </tr>
        </thead>
        <tbody className="bg-white divide-y divide-gray-200">
          {alarms.map(alarm => {
            const formatted = formatAlarmNotification(alarm)
            const isProcessing = processingAlarmId === alarm.id
            const canAcknowledge = user && alarm.status === 'received'
            const canResolve = user && ['received', 'acknowledged'].includes(alarm.status)

            return (
              <tr key={alarm.id} className="hover:bg-gray-50">
                <td className="px-3 py-3 text-sm text-gray-900">
                  <div className="font-medium truncate" title={alarm.building?.name || 'Unknown'}>
                    {alarm.building?.name || 'Unknown'}
                  </div>
                  {alarm.building?.building_code && (
                    <div className="text-gray-500 text-xs truncate" title={alarm.building?.building_code}>
                      {alarm.building?.building_code}
                    </div>
                  )}
                </td>

                <td className="px-3 py-3 text-sm text-gray-900">
                  <div className="truncate" title={alarm.alarm_type?.name || 'Unknown'}>
                    {alarm.alarm_type?.name || 'Unknown'}
                  </div>
                </td>

                <td className="px-3 py-3 text-sm">
                  <div className="flex items-center">
                    <div
                      className="w-3 h-3 rounded-full mr-2 flex-shrink-0"
                      style={{ backgroundColor: alarm.severity?.color || '#6b7280' }}
                    ></div>
                    <span className="font-medium truncate" title={alarm.severity?.name || 'Unknown'}>
                      {alarm.severity?.name || 'Unknown'}
                    </span>
                  </div>
                </td>

                <td className="px-3 py-3 text-sm text-gray-900">
                  <div className="truncate" title={formatted.formattedTime}>
                    {formatted.formattedTime}
                  </div>
                  <div className="text-gray-500 text-xs truncate" title={`Received: ${new Date(alarm.created_at).toLocaleTimeString()}`}>
                    Received: {new Date(alarm.created_at).toLocaleTimeString()}
                  </div>
                </td>

                <td className="px-3 py-3">
                  <span
                    className="inline-flex px-2 py-1 text-xs font-semibold rounded-full whitespace-nowrap"
                    style={{
                      backgroundColor: formatted.statusColor + '20',
                      color: formatted.statusColor
                    }}
                  >
                    {alarm.status.replace('_', ' ').toUpperCase()}
                  </span>
                </td>

                <td className="px-3 py-3 text-sm text-gray-900">
                  <div className="line-clamp-2 cursor-help" title={alarm.alarm_details}>
                    {alarm.alarm_details || 'No details available'}
                  </div>
                  {alarm.location_details && (
                    <div className="text-gray-500 text-xs line-clamp-1 cursor-help mt-1" title={alarm.location_details}>
                      📍 {alarm.location_details}
                    </div>
                  )}
                </td>

                <td className="px-3 py-3 text-right text-sm font-medium">
                  <div className="flex justify-end gap-1 flex-wrap">
                    {canAcknowledge && (
                      <button
                        onClick={() => onAcknowledge(alarm.id)}
                        disabled={isProcessing}
                        className="text-yellow-600 hover:text-yellow-900 disabled:opacity-50 text-xs px-2 py-1 border border-yellow-300 rounded hover:bg-yellow-50 whitespace-nowrap"
                      >
                        {isProcessing ? '...' : 'Ack'}
                      </button>
                    )}

                    {canResolve && (
                      <button
                        onClick={() => onResolve(alarm.id)}
                        disabled={isProcessing}
                        className="text-green-600 hover:text-green-900 disabled:opacity-50 text-xs px-2 py-1 border border-green-300 rounded hover:bg-green-50 whitespace-nowrap"
                      >
                        {isProcessing ? '...' : 'Resolve'}
                      </button>
                    )}
                  </div>
                </td>
              </tr>
            )
          })}
        </tbody>
      </table>
    </div>
  )
}

// Individual alarm card component
const AlarmCard = ({ alarm, onAcknowledge, onResolve, isProcessing, canAcknowledge, canResolve }) => {
  return (
    <div className="p-4 hover:bg-gray-50">
      <div className="flex items-start justify-between gap-4">
        <div className="flex-1 min-w-0">
          {/* Header */}
          <div className="flex items-center gap-3 mb-3 flex-wrap">
            <div
              className="w-3 h-3 rounded-full flex-shrink-0"
              style={{ backgroundColor: alarm.severityColor }}
            ></div>
            <h3 className="font-medium text-gray-900 flex-1 min-w-0 truncate">
              {alarm.subject || 'Alarm Notification'}
            </h3>
            <span
              className="px-2 py-1 text-xs font-medium rounded-full whitespace-nowrap"
              style={{
                backgroundColor: alarm.statusColor + '20',
                color: alarm.statusColor
              }}
            >
              {alarm.status.replace('_', ' ').toUpperCase()}
            </span>
          </div>

          {/* Details - Enhanced layout for wider screens */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 text-sm text-gray-600 mb-3">
            <div className="space-y-1">
              <p><strong>Building:</strong> <span className="break-words">{alarm.building?.name || 'Unknown'}</span></p>
              <p><strong>Type:</strong> <span className="break-words">{alarm.alarm_type?.name || 'Unknown'}</span></p>
              <p><strong>Severity:</strong> <span className="break-words">{alarm.severity?.name || 'Unknown'}</span></p>
            </div>
            <div className="space-y-1">
              <p><strong>Alarm Time:</strong> <span className="break-words">{alarm.formattedTime}</span></p>
              <p><strong>Received:</strong> <span className="break-words">{alarm.formattedCreatedAt}</span></p>
              <p><strong>From:</strong> <span className="break-words">{alarm.sender_email}</span></p>
            </div>
            {alarm.location_details && (
              <div className="space-y-1">
                <p><strong>Location:</strong> <span className="break-words">{alarm.location_details}</span></p>
              </div>
            )}
          </div>

          {/* Alarm Details */}
          {alarm.alarm_details && (
            <div className="bg-gray-50 rounded p-3 mb-3">
              <p className="text-sm text-gray-700 break-words">{alarm.alarm_details}</p>
            </div>
          )}
        </div>

        {/* Actions */}
        <div className="flex flex-col gap-2 flex-shrink-0">
          {canAcknowledge && (
            <button
              onClick={onAcknowledge}
              disabled={isProcessing}
              className="px-3 py-1 text-sm bg-yellow-100 text-yellow-800 rounded hover:bg-yellow-200 disabled:opacity-50 whitespace-nowrap"
            >
              {isProcessing ? 'Processing...' : 'Acknowledge'}
            </button>
          )}

          {canResolve && (
            <button
              onClick={onResolve}
              disabled={isProcessing}
              className="px-3 py-1 text-sm bg-green-100 text-green-800 rounded hover:bg-green-200 disabled:opacity-50 whitespace-nowrap"
            >
              {isProcessing ? 'Processing...' : 'Resolve'}
            </button>
          )}
        </div>
      </div>
    </div>
  )
}

export default AlarmDashboard
